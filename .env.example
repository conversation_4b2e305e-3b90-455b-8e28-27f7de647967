# Production Environment Configuration
# Copy this file to .env and update the values as needed

# Database Configuration
POSTGRES_DB=indezy
POSTGRES_USER=indezy_user
POSTGRES_PASSWORD=change_this_password_in_production
POSTGRES_PORT=5432

# Application Ports
BACKEND_PORT=8080
FRONTEND_PORT=4200

# pgAdmin Configuration (Optional - only used with --profile admin)
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=change_this_password_in_production
PGADMIN_PORT=5050
