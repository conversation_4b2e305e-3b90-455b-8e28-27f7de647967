{
  // Development Container Configuration
  // Uses the unified docker-compose.yml in the root directory
  "name": "Indezy Development Environment",
  "dockerComposeFile": "../docker-compose.yml",
  "service": "devcontainer",
  "workspaceFolder": "/workspace",
  "runServices": ["postgres", "pgadmin"],
  "customizations": {
    "vscode": {
      "settings": {
        "java.configuration.runtimes": [
          {
            "name": "JavaSE-21",
            "path": "/usr/local/sdkman/candidates/java/current"
          }
        ],
        "java.compile.nullAnalysis.mode": "automatic",
        "java.configuration.maven.userSettings": "/workspace/.devcontainer/maven-settings.xml"
      }
    }
  },
  "forwardPorts": [8080, 4200, 5432, 5050],
  "portsAttributes": {
    "8080": {
      "label": "Spring Boot Backend",
      "onAutoForward": "notify"
    },
    "4200": {
      "label": "Angular Frontend",
      "onAutoForward": "openBrowser"
    },
    "5432": {
      "label": "PostgreSQL Database",
      "onAutoForward": "silent"
    },
    "5050": {
      "label": "pgAdmin",
      "onAutoForward": "notify"
    }
  },
  "postCreateCommand": "bash .devcontainer/post-create.sh",
  "remoteUser": "vscode",
  "shutdownAction": "stopCompose"
}
