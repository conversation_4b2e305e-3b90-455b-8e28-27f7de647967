{
  "recommendations": [
    // Java Development
    "vscjava.vscode-java-pack",
    "vscjava.vscode-spring-boot-dashboard",
    "vmware.vscode-spring-boot",
    "vscjava.vscode-maven",
    "sonarsource.sonarlint-vscode",

    // Angular/TypeScript Development
    "angular.ng-template",
    "ms-vscode.vscode-typescript-next",
    "johnpapa.angular2",
    "cyrilletuzi.angular-schematics",
    "ms-vscode.vscode-eslint",
    "esbenp.prettier-vscode",

    // CSS/Styling
    "bradlc.vscode-tailwindcss",

    // General Development
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-azuretools.vscode-docker",
    "ms-vscode.vscode-postgresql",
    "humao.rest-client",

    // Code Quality & Testing
    "ms-vscode.test-adapter-converter",
    "hbenl.vscode-test-explorer",
    "kavod-io.vscode-jest-test-adapter",

    // Git & Version Control
    "eamodio.gitlens",
    "github.vscode-pull-request-github",

    // Productivity
    "ms-vscode.vscode-todo-highlight",
    "streetsidesoftware.code-spell-checker",
    "ms-vscode.vscode-markdown-preview-enhanced",
    "bierner.markdown-mermaid",
    "augment.vscode-augment"
  ]
}
