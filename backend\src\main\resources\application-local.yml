server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: indezy-backend
  
  datasource:
    url: jdbc:h2:mem:indezy;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: password
    driver-class-name: org.h2.Driver
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        show_sql: true
    defer-datasource-initialization: true
  
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: true
  
  sql:
    init:
      mode: always
      data-locations: classpath:data-local.sql
      continue-on-error: false
  
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

  security:
    oauth2:
      client:
        registration:
          google:
            client-id: ${GOOGLE_CLIENT_ID:your-google-client-id}
            client-secret: ${GOOGLE_CLIENT_SECRET:your-google-client-secret}
            scope:
              - openid
              - profile
              - email
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
          github:
            client-id: ${GITHUB_CLIENT_ID:your-github-client-id}
            client-secret: ${GITHUB_CLIENT_SECRET:your-github-client-secret}
            scope:
              - user:email
              - read:user
          microsoft:
            client-id: ${MICROSOFT_CLIENT_ID:your-microsoft-client-id}
            client-secret: ${MICROSOFT_CLIENT_SECRET:your-microsoft-client-secret}
            scope:
              - openid
              - profile
              - email
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
        provider:
          microsoft:
            authorization-uri: https://login.microsoftonline.com/common/oauth2/v2.0/authorize
            token-uri: https://login.microsoftonline.com/common/oauth2/v2.0/token
            user-info-uri: https://graph.microsoft.com/v1.0/me
            user-name-attribute: id

jwt:
  secret: ${JWT_SECRET:indezy-local-h2-secret-key-for-development-only}
  expiration: 86400000 # 24 hours in milliseconds

cors:
  allowed-origins:
    - http://localhost:4200
    - http://127.0.0.1:4200
    - http://localhost:50744
    - http://127.0.0.1:50744
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers:
    - "*"
  allow-credentials: true

logging:
  level:
    dev.byteworks.indezy: DEBUG
    org.springframework.security: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.web: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
